预约服务与管理
支持移动端试验内容的预约功能，借助试验登记的车辆信息和对应的试验内容，通过查看试验场地的排期，结合天气情况预定某个时间段的场地。需要运营管理系统中试验内容、排期管理、预约管理等模块功能的支撑。


我会创建预约服务相关的实体类。根据需求，我们需要创建以下实体：

TestSite (试验场地)
SiteSchedule (场地排期)
SiteBooking (场地预约)
WeatherRecord (天气记录)
让我来创建这些实体类：

Created TestSite.java

Created SiteSchedule.java

Created SiteBooking.java

Created WeatherRecord.java

我已经创建了4个实体类，它们的关系如下：

TestSite (试验场地)

基本信息：场地名称、编号、面积等
场地类型和状态
设施和位置信息
SiteSchedule (场地排期)

关联场地ID（不使用JoinColumn）
时间段安排
可用状态和剩余数量
SiteBooking (场地预约)

关联试验内容ID、场地ID、排期ID（不使用JoinColumn）
预约时间段
预约状态跟踪
关联车辆和用户信息
关联天气记录
WeatherRecord (天气记录)

关联场地ID（不使用JoinColumn）
详细天气信息
是否适合测试的判断
设计特点：

避免使用 @JoinColumn，改用字符串ID关联
使用 UUID 作为主键
包含完整的状态管理
添加了必要的约束和长度限制
使用枚举类型管理状态和类型
继承 BaseEntity 获取通用字段
使用 Lombok 注解简化代码
这个设计支持：

场地信息管理
场地排期查看
根据天气预约场地
试验内容与场地预约关联
完整的预约状态跟踪