预约管理
移动端可查看场地排期，对客户的预约做出审批、驳回、改期等操作。需要运营管理系统中排期管理、预约管理等模块功能的支撑。

我已经创建了两个新的实体类来支持预约管理功能：

BookingReview (预约审批记录)

记录预约的审批状态和过程
包含审批意见和特殊要求
记录驳回原因和优先级
支持后续跟进标记
BookingChange (预约变更记录)

记录预约的所有变更请求
支持多种变更类型（改期、换场地等）
记录原始和新的时间/场地信息
完整的变更流程状态跟踪
设计特点：

使用字符串ID而不是JoinColumn进行关联
完整的状态管理和审批流程
详细的时间记录
灵活的变更类型支持
完整的审批追踪
支持备注和原因说明
这个设计支持以下功能：

预约审批管理

审批状态跟踪
审批意见记录
驳回原因说明
优先级管理
预约变更管理

改期处理
场地变更
时间调整
预约取消
变更审批流程

变更申请记录
变更审批流程
完整的状态跟踪
原因说明
这些实体类配合之前创建的SiteBooking和SiteSchedule实体，可以完整支持预约管理功能。