个人中心
根据基础测试服务登记的员工信息，依据工号、姓名、手机号、身份证号、面部信息等信息进行身份鉴权。需要基础服务系统中用户管理模块功能的支撑。


我已经创建了两个新的实体类：

EmployeeInfo (员工信息)

关联用户ID
包含工号、身份证号等基本信息
包含入职信息和部门信息
包含员工状态管理
包含紧急联系人信息
支持资质证书记录
FaceAuth (面部认证信息)

关联用户ID
存储面部图像和特征数据
完整的认证状态管理
记录认证时间和更新历史
支持元数据存储
设计特点：

使用字符串ID而不是JoinColumn进行关联
合理的字段长度限制
使用枚举管理状态
完整的时间记录
继承BaseEntity获取通用字段
这个设计支持以下功能：

员工身份认证

工号验证
身份证验证
面部识别
手机号验证
员工信息管理

基本信息维护
状态管理
部门岗位管理
资质管理
面部认证

面部特征注册
认证状态管理
验证历史记录
安全元数据存储


让我修改 User 实体，并创建一个 PersonInfo 实体来存储普通用户的信息：

Created PersonType.java

User.java+2-5
User.java+2-5
Created PersonInfo.java

现在我们有了完整的用户体系：

User (基础用户实体)

包含共用的基本信息（用户名、密码、手机号等）
使用 PersonType 区分不同类型的用户
支持基础的认证信息
EmployeeInfo (员工信息)

专门存储员工特有信息
包含工号、部门等企业内部信息
支持员工状态管理
PersonInfo (普通用户信息)

存储外部用户的详细信息
支持多种用户类型（客户、供应商等）
包含企业用户所需的字段
支持身份验证和资质管理
FaceAuth (面部认证)

支持所有类型用户的面部认证
通用的认证状态管理
主要特点：

统一的用户管理体系
灵活的用户类型支持
完整的认证信息管理
清晰的身份区分
完整的状态跟踪
支持多种认证方式
这个设计支持：

区分内部员工和外部用户
支持不同类型用户的特定信息存储
统一的认证管理
灵活的用户状态管理
完整的资质和证件管理