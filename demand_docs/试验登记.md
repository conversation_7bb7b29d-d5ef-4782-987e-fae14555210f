试验登记
支持移动端提交试验任务的登记功能。基于授权人授权日期、企业的合同信息等，可在移动端创建试验任务，包括该试验任务的项目信息、车辆信息以及车辆对应的试验内容等。需要运营管理系统中项目管理、车辆管理、试验内容等模块功能的支撑。

我已经创建了4个实体类，它们的关系如下：

TestTask (试验任务)

包含基本任务信息：任务编号、授权人、授权日期
关联合同信息 (Contract)
包含多个试验项目 (OneToMany -> TestProject)
包含任务状态跟踪
TestProject (试验项目)

关联到试验任务 (ManyToOne -> TestTask)
包含项目基本信息：名称、编号、要求等
关联多个试验车辆 (OneToMany -> TaskVehicle)
TaskVehicle (任务车辆关联)

关联试验项目 (ManyToOne -> TestProject)
关联车辆信息 (ManyToOne -> Vehicle)
包含多个试验内容 (OneToMany -> TestContent)
TestContent (试验内容)

关联到任务车辆 (ManyToOne -> TaskVehicle)
包含具体的测试项目、描述、时间安排等
包含测试结果和状态跟踪
这些实体类的设计特点：

使用了JPA注解进行ORM映射
继承自BaseEntity以获取通用字段
使用了Lombok简化代码
包含了完整的状态管理
使用UUID作为主键
建立了正确的实体间关系
添加了适当的字段长度限制
使用了枚举类型管理状态