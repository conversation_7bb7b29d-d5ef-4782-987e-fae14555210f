{"name": "car-service-mobile", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"axios": "^1.4.0", "pinia": "^2.1.4", "vant": "^4.6.0", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "less": "^4.1.3", "less-loader": "^11.1.3", "postcss-px-to-viewport": "^1.1.1", "prettier": "^3.0.0", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.6"}}