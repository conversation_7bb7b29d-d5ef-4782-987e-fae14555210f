package com.carservice.controller;

import com.carservice.common.api.ApiResponse;
import com.carservice.service.DashboardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 移动端仪表板控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/dashboard")
@RequiredArgsConstructor
@Tag(name = "移动端仪表板", description = "移动端首页数据、统计信息等功能")
public class DashboardController {
    
    private final DashboardService dashboardService;
    
    /**
     * 获取用户仪表板数据
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户仪表板数据", description = "获取用户首页展示的各种统计数据")
    public ResponseEntity<ApiResponse<UserDashboardDTO>> getUserDashboard(@PathVariable String userId) {
        try {
            UserDashboardDTO dashboard = dashboardService.getUserDashboard(userId);
            log.info("获取用户仪表板数据成功: {}", userId);
            return ResponseEntity.ok(ApiResponse.success(dashboard));
        } catch (Exception e) {
            log.error("获取用户仪表板数据失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/user/{userId}/stats")
    @Operation(summary = "获取用户统计信息")
    public ResponseEntity<ApiResponse<UserStatsDTO>> getUserStats(
            @PathVariable String userId,
            @Parameter(description = "统计开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "统计结束时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            UserStatsDTO stats = dashboardService.getUserStats(userId, startTime, endTime);
            log.info("获取用户统计信息成功: 用户={}, 时间范围: {} - {}", userId, startTime, endTime);
            return ResponseEntity.ok(ApiResponse.success(stats));
        } catch (Exception e) {
            log.error("获取用户统计信息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取最近活动
     */
    @GetMapping("/user/{userId}/recent-activities")
    @Operation(summary = "获取最近活动")
    public ResponseEntity<ApiResponse<List<ActivityDTO>>> getRecentActivities(
            @PathVariable String userId,
            @Parameter(description = "活动数量限制") @RequestParam(defaultValue = "10") int limit) {
        try {
            List<ActivityDTO> activities = dashboardService.getRecentActivities(userId, limit);
            log.info("获取最近活动成功: 用户={}, 限制数量={}", userId, limit);
            return ResponseEntity.ok(ApiResponse.success(activities));
        } catch (Exception e) {
            log.error("获取最近活动失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取待办事项
     */
    @GetMapping("/user/{userId}/todos")
    @Operation(summary = "获取待办事项")
    public ResponseEntity<ApiResponse<List<TodoDTO>>> getUserTodos(@PathVariable String userId) {
        try {
            List<TodoDTO> todos = dashboardService.getUserTodos(userId);
            log.info("获取待办事项成功: {}", userId);
            return ResponseEntity.ok(ApiResponse.success(todos));
        } catch (Exception e) {
            log.error("获取待办事项失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取快捷操作
     */
    @GetMapping("/user/{userId}/quick-actions")
    @Operation(summary = "获取快捷操作")
    public ResponseEntity<ApiResponse<List<QuickActionDTO>>> getQuickActions(@PathVariable String userId) {
        try {
            List<QuickActionDTO> actions = dashboardService.getQuickActions(userId);
            log.info("获取快捷操作成功: {}", userId);
            return ResponseEntity.ok(ApiResponse.success(actions));
        } catch (Exception e) {
            log.error("获取快捷操作失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取系统公告
     */
    @GetMapping("/announcements")
    @Operation(summary = "获取系统公告")
    public ResponseEntity<ApiResponse<List<AnnouncementDTO>>> getAnnouncements(
            @Parameter(description = "公告数量限制") @RequestParam(defaultValue = "5") int limit) {
        try {
            List<AnnouncementDTO> announcements = dashboardService.getAnnouncements(limit);
            log.info("获取系统公告成功: 限制数量={}", limit);
            return ResponseEntity.ok(ApiResponse.success(announcements));
        } catch (Exception e) {
            log.error("获取系统公告失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取天气信息
     */
    @GetMapping("/weather")
    @Operation(summary = "获取天气信息")
    public ResponseEntity<ApiResponse<WeatherInfoDTO>> getWeatherInfo(
            @Parameter(description = "位置") @RequestParam(required = false) String location) {
        try {
            WeatherInfoDTO weather = dashboardService.getWeatherInfo(location);
            log.info("获取天气信息成功: 位置={}", location);
            return ResponseEntity.ok(ApiResponse.success(weather));
        } catch (Exception e) {
            log.error("获取天气信息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 用户仪表板DTO
     */
    public static class UserDashboardDTO {
        private String userId;
        private String userName;
        private String userAvatar;
        private UserStatsDTO stats;
        private List<ActivityDTO> recentActivities;
        private List<TodoDTO> todos;
        private List<QuickActionDTO> quickActions;
        private List<AnnouncementDTO> announcements;
        private WeatherInfoDTO weather;
        
        // Getters and Setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public String getUserAvatar() { return userAvatar; }
        public void setUserAvatar(String userAvatar) { this.userAvatar = userAvatar; }
        public UserStatsDTO getStats() { return stats; }
        public void setStats(UserStatsDTO stats) { this.stats = stats; }
        public List<ActivityDTO> getRecentActivities() { return recentActivities; }
        public void setRecentActivities(List<ActivityDTO> recentActivities) { this.recentActivities = recentActivities; }
        public List<TodoDTO> getTodos() { return todos; }
        public void setTodos(List<TodoDTO> todos) { this.todos = todos; }
        public List<QuickActionDTO> getQuickActions() { return quickActions; }
        public void setQuickActions(List<QuickActionDTO> quickActions) { this.quickActions = quickActions; }
        public List<AnnouncementDTO> getAnnouncements() { return announcements; }
        public void setAnnouncements(List<AnnouncementDTO> announcements) { this.announcements = announcements; }
        public WeatherInfoDTO getWeather() { return weather; }
        public void setWeather(WeatherInfoDTO weather) { this.weather = weather; }
    }
    
    /**
     * 用户统计DTO
     */
    public static class UserStatsDTO {
        private Integer totalBookings;
        private Integer pendingBookings;
        private Integer completedBookings;
        private Integer totalVehicles;
        private Integer unreadMessages;
        private Integer pendingApprovals;
        
        // Getters and Setters
        public Integer getTotalBookings() { return totalBookings; }
        public void setTotalBookings(Integer totalBookings) { this.totalBookings = totalBookings; }
        public Integer getPendingBookings() { return pendingBookings; }
        public void setPendingBookings(Integer pendingBookings) { this.pendingBookings = pendingBookings; }
        public Integer getCompletedBookings() { return completedBookings; }
        public void setCompletedBookings(Integer completedBookings) { this.completedBookings = completedBookings; }
        public Integer getTotalVehicles() { return totalVehicles; }
        public void setTotalVehicles(Integer totalVehicles) { this.totalVehicles = totalVehicles; }
        public Integer getUnreadMessages() { return unreadMessages; }
        public void setUnreadMessages(Integer unreadMessages) { this.unreadMessages = unreadMessages; }
        public Integer getPendingApprovals() { return pendingApprovals; }
        public void setPendingApprovals(Integer pendingApprovals) { this.pendingApprovals = pendingApprovals; }
    }
    
    /**
     * 活动DTO
     */
    public static class ActivityDTO {
        private String activityId;
        private String activityType;
        private String title;
        private String description;
        private String status;
        private LocalDateTime activityTime;
        
        // Getters and Setters
        public String getActivityId() { return activityId; }
        public void setActivityId(String activityId) { this.activityId = activityId; }
        public String getActivityType() { return activityType; }
        public void setActivityType(String activityType) { this.activityType = activityType; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public LocalDateTime getActivityTime() { return activityTime; }
        public void setActivityTime(LocalDateTime activityTime) { this.activityTime = activityTime; }
    }
    
    /**
     * 待办事项DTO
     */
    public static class TodoDTO {
        private String todoId;
        private String title;
        private String description;
        private String priority;
        private LocalDateTime dueDate;
        private Boolean completed;
        
        // Getters and Setters
        public String getTodoId() { return todoId; }
        public void setTodoId(String todoId) { this.todoId = todoId; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getPriority() { return priority; }
        public void setPriority(String priority) { this.priority = priority; }
        public LocalDateTime getDueDate() { return dueDate; }
        public void setDueDate(LocalDateTime dueDate) { this.dueDate = dueDate; }
        public Boolean getCompleted() { return completed; }
        public void setCompleted(Boolean completed) { this.completed = completed; }
    }
    
    /**
     * 快捷操作DTO
     */
    public static class QuickActionDTO {
        private String actionId;
        private String title;
        private String description;
        private String icon;
        private String actionUrl;
        private String actionType;
        
        // Getters and Setters
        public String getActionId() { return actionId; }
        public void setActionId(String actionId) { this.actionId = actionId; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getIcon() { return icon; }
        public void setIcon(String icon) { this.icon = icon; }
        public String getActionUrl() { return actionUrl; }
        public void setActionUrl(String actionUrl) { this.actionUrl = actionUrl; }
        public String getActionType() { return actionType; }
        public void setActionType(String actionType) { this.actionType = actionType; }
    }
    
    /**
     * 系统公告DTO
     */
    public static class AnnouncementDTO {
        private String announcementId;
        private String title;
        private String content;
        private String priority;
        private LocalDateTime publishTime;
        private LocalDateTime expireTime;
        
        // Getters and Setters
        public String getAnnouncementId() { return announcementId; }
        public void setAnnouncementId(String announcementId) { this.announcementId = announcementId; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        public String getPriority() { return priority; }
        public void setPriority(String priority) { this.priority = priority; }
        public LocalDateTime getPublishTime() { return publishTime; }
        public void setPublishTime(LocalDateTime publishTime) { this.publishTime = publishTime; }
        public LocalDateTime getExpireTime() { return expireTime; }
        public void setExpireTime(LocalDateTime expireTime) { this.expireTime = expireTime; }
    }
    
    /**
     * 天气信息DTO
     */
    public static class WeatherInfoDTO {
        private String location;
        private String weather;
        private String temperature;
        private String humidity;
        private String windSpeed;
        private String description;
        private String icon;
        
        // Getters and Setters
        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }
        public String getWeather() { return weather; }
        public void setWeather(String weather) { this.weather = weather; }
        public String getTemperature() { return temperature; }
        public void setTemperature(String temperature) { this.temperature = temperature; }
        public String getHumidity() { return humidity; }
        public void setHumidity(String humidity) { this.humidity = humidity; }
        public String getWindSpeed() { return windSpeed; }
        public void setWindSpeed(String windSpeed) { this.windSpeed = windSpeed; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getIcon() { return icon; }
        public void setIcon(String icon) { this.icon = icon; }
    }
}
