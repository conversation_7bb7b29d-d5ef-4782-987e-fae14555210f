package com.carservice.controller;

import com.carservice.common.api.ApiResponse;
import com.carservice.service.MobileVehicleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 移动端车辆管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/mobile/vehicles")
@RequiredArgsConstructor
@Tag(name = "移动端车辆管理", description = "移动端车辆信息查询、管理等功能")
public class MobileVehicleController {
    
    private final MobileVehicleService mobileVehicleService;
    
    /**
     * 获取车辆列表
     */
    @GetMapping
    @Operation(summary = "获取车辆列表", description = "支持按品牌、型号、状态筛选")
    public ResponseEntity<ApiResponse<Page<VehicleDTO>>> getVehicleList(
            @Parameter(description = "车辆品牌") @RequestParam(required = false) String brand,
            @Parameter(description = "车辆型号") @RequestParam(required = false) String model,
            @Parameter(description = "车辆状态") @RequestParam(required = false) String status,
            @Parameter(description = "关键词搜索") @RequestParam(required = false) String keyword,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<VehicleDTO> vehicles = mobileVehicleService.getVehicleList(brand, model, status, keyword, pageable);
            log.info("获取车辆列表成功: 品牌={}, 型号={}, 状态={}, 关键词={}", brand, model, status, keyword);
            return ResponseEntity.ok(ApiResponse.success(vehicles));
        } catch (Exception e) {
            log.error("获取车辆列表失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取车辆详情
     */
    @GetMapping("/{vehicleId}")
    @Operation(summary = "获取车辆详情")
    public ResponseEntity<ApiResponse<VehicleDTO>> getVehicleDetail(@PathVariable String vehicleId) {
        try {
            VehicleDTO vehicle = mobileVehicleService.getVehicleDetail(vehicleId);
            log.info("获取车辆详情成功: {}", vehicleId);
            return ResponseEntity.ok(ApiResponse.success(vehicle));
        } catch (Exception e) {
            log.error("获取车辆详情失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取用户车辆列表
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户车辆列表")
    public ResponseEntity<ApiResponse<List<VehicleDTO>>> getUserVehicles(@PathVariable String userId) {
        try {
            List<VehicleDTO> vehicles = mobileVehicleService.getUserVehicles(userId);
            log.info("获取用户车辆列表成功: {}", userId);
            return ResponseEntity.ok(ApiResponse.success(vehicles));
        } catch (Exception e) {
            log.error("获取用户车辆列表失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 添加用户车辆
     */
    @PostMapping("/user/{userId}")
    @Operation(summary = "添加用户车辆")
    public ResponseEntity<ApiResponse<VehicleDTO>> addUserVehicle(
            @PathVariable String userId,
            @Valid @RequestBody VehicleDTO vehicleDTO) {
        try {
            VehicleDTO vehicle = mobileVehicleService.addUserVehicle(userId, vehicleDTO);
            log.info("添加用户车辆成功: 用户={}, 车牌号={}", userId, vehicleDTO.getLicensePlate());
            return ResponseEntity.ok(ApiResponse.success(vehicle, "车辆添加成功"));
        } catch (Exception e) {
            log.error("添加用户车辆失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 更新车辆信息
     */
    @PutMapping("/{vehicleId}")
    @Operation(summary = "更新车辆信息")
    public ResponseEntity<ApiResponse<VehicleDTO>> updateVehicle(
            @PathVariable String vehicleId,
            @Valid @RequestBody VehicleDTO vehicleDTO) {
        try {
            VehicleDTO vehicle = mobileVehicleService.updateVehicle(vehicleId, vehicleDTO);
            log.info("更新车辆信息成功: {}", vehicleId);
            return ResponseEntity.ok(ApiResponse.success(vehicle, "车辆信息更新成功"));
        } catch (Exception e) {
            log.error("更新车辆信息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 删除车辆信息
     */
    @DeleteMapping("/{vehicleId}")
    @Operation(summary = "删除车辆信息")
    public ResponseEntity<ApiResponse<Void>> deleteVehicle(@PathVariable String vehicleId) {
        try {
            mobileVehicleService.deleteVehicle(vehicleId);
            log.info("删除车辆信息成功: {}", vehicleId);
            return ResponseEntity.ok(ApiResponse.success(null, "车辆删除成功"));
        } catch (Exception e) {
            log.error("删除车辆信息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 上传车辆图片
     */
    @PostMapping("/{vehicleId}/images")
    @Operation(summary = "上传车辆图片")
    public ResponseEntity<ApiResponse<List<String>>> uploadVehicleImages(
            @PathVariable String vehicleId,
            @RequestParam("files") List<MultipartFile> files) {
        try {
            List<String> imageUrls = mobileVehicleService.uploadVehicleImages(vehicleId, files);
            log.info("上传车辆图片成功: 车辆={}, 图片数量={}", vehicleId, files.size());
            return ResponseEntity.ok(ApiResponse.success(imageUrls, "车辆图片上传成功"));
        } catch (Exception e) {
            log.error("上传车辆图片失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取车辆图片列表
     */
    @GetMapping("/{vehicleId}/images")
    @Operation(summary = "获取车辆图片列表")
    public ResponseEntity<ApiResponse<List<VehicleImageDTO>>> getVehicleImages(@PathVariable String vehicleId) {
        try {
            List<VehicleImageDTO> images = mobileVehicleService.getVehicleImages(vehicleId);
            log.info("获取车辆图片列表成功: {}", vehicleId);
            return ResponseEntity.ok(ApiResponse.success(images));
        } catch (Exception e) {
            log.error("获取车辆图片列表失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取车辆品牌列表
     */
    @GetMapping("/brands")
    @Operation(summary = "获取车辆品牌列表")
    public ResponseEntity<ApiResponse<List<VehicleBrandDTO>>> getVehicleBrands() {
        try {
            List<VehicleBrandDTO> brands = mobileVehicleService.getVehicleBrands();
            log.info("获取车辆品牌列表成功");
            return ResponseEntity.ok(ApiResponse.success(brands));
        } catch (Exception e) {
            log.error("获取车辆品牌列表失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取车辆型号列表
     */
    @GetMapping("/models")
    @Operation(summary = "获取车辆型号列表")
    public ResponseEntity<ApiResponse<List<VehicleModelDTO>>> getVehicleModels(
            @Parameter(description = "品牌ID") @RequestParam(required = false) String brandId) {
        try {
            List<VehicleModelDTO> models = mobileVehicleService.getVehicleModels(brandId);
            log.info("获取车辆型号列表成功: 品牌={}", brandId);
            return ResponseEntity.ok(ApiResponse.success(models));
        } catch (Exception e) {
            log.error("获取车辆型号列表失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * 车辆DTO
     */
    public static class VehicleDTO {
        private String vehicleId;
        private String licensePlate;
        private String brand;
        private String model;
        private String year;
        private String color;
        private String engineNumber;
        private String chassisNumber;
        private String status;
        private String description;
        private LocalDateTime createdTime;
        
        // Getters and Setters
        public String getVehicleId() { return vehicleId; }
        public void setVehicleId(String vehicleId) { this.vehicleId = vehicleId; }
        public String getLicensePlate() { return licensePlate; }
        public void setLicensePlate(String licensePlate) { this.licensePlate = licensePlate; }
        public String getBrand() { return brand; }
        public void setBrand(String brand) { this.brand = brand; }
        public String getModel() { return model; }
        public void setModel(String model) { this.model = model; }
        public String getYear() { return year; }
        public void setYear(String year) { this.year = year; }
        public String getColor() { return color; }
        public void setColor(String color) { this.color = color; }
        public String getEngineNumber() { return engineNumber; }
        public void setEngineNumber(String engineNumber) { this.engineNumber = engineNumber; }
        public String getChassisNumber() { return chassisNumber; }
        public void setChassisNumber(String chassisNumber) { this.chassisNumber = chassisNumber; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public LocalDateTime getCreatedTime() { return createdTime; }
        public void setCreatedTime(LocalDateTime createdTime) { this.createdTime = createdTime; }
    }
    
    /**
     * 车辆图片DTO
     */
    public static class VehicleImageDTO {
        private String imageId;
        private String vehicleId;
        private String imageUrl;
        private String imageType;
        private String description;
        private LocalDateTime uploadTime;
        
        // Getters and Setters
        public String getImageId() { return imageId; }
        public void setImageId(String imageId) { this.imageId = imageId; }
        public String getVehicleId() { return vehicleId; }
        public void setVehicleId(String vehicleId) { this.vehicleId = vehicleId; }
        public String getImageUrl() { return imageUrl; }
        public void setImageUrl(String imageUrl) { this.imageUrl = imageUrl; }
        public String getImageType() { return imageType; }
        public void setImageType(String imageType) { this.imageType = imageType; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public LocalDateTime getUploadTime() { return uploadTime; }
        public void setUploadTime(LocalDateTime uploadTime) { this.uploadTime = uploadTime; }
    }
    
    /**
     * 车辆品牌DTO
     */
    public static class VehicleBrandDTO {
        private String brandId;
        private String brandName;
        private String logoUrl;
        
        // Getters and Setters
        public String getBrandId() { return brandId; }
        public void setBrandId(String brandId) { this.brandId = brandId; }
        public String getBrandName() { return brandName; }
        public void setBrandName(String brandName) { this.brandName = brandName; }
        public String getLogoUrl() { return logoUrl; }
        public void setLogoUrl(String logoUrl) { this.logoUrl = logoUrl; }
    }
    
    /**
     * 车辆型号DTO
     */
    public static class VehicleModelDTO {
        private String modelId;
        private String brandId;
        private String modelName;
        private String specifications;
        
        // Getters and Setters
        public String getModelId() { return modelId; }
        public void setModelId(String modelId) { this.modelId = modelId; }
        public String getBrandId() { return brandId; }
        public void setBrandId(String brandId) { this.brandId = brandId; }
        public String getModelName() { return modelName; }
        public void setModelName(String modelName) { this.modelName = modelName; }
        public String getSpecifications() { return specifications; }
        public void setSpecifications(String specifications) { this.specifications = specifications; }
    }
}
