# 车辆管理系统

## 项目概述

本项目是一个基于前后端分离架构的车辆管理系统，旨在提供车辆信息管理、用户管理、维修保养管理、预约试驾等功能。系统采用移动端优先的设计理念，同时兼容PC端访问。

## 技术栈

### 前端
- 移动端：React Native
- 后台管理：React + Ant Design
- 数据可视化：ECharts

### 后端
- 框架：Spring Boot
- 数据库：MySQL
- 缓存：Redis
- 消息队列：RabbitMQ（可选）
- 搜索引擎：Elasticsearch（可选）

### 部署
- 容器化：Docker
- CI/CD：Jenkins

## 系统架构

```
+------------------+    +------------------+    +------------------+
|                  |    |                  |    |                  |
|  移动端应用      |    |  Web管理后台     |    |  第三方服务      |
|  (React Native)  |    |  (React)         |    |  (支付/地图等)   |
|                  |    |                  |    |                  |
+--------+---------+    +--------+---------+    +--------+---------+
         |                       |                       |
         v                       v                       v
+------------------+    +------------------+    +------------------+
|                  |    |                  |    |                  |
|  API网关         |<-->|  认证授权服务    |<-->|  用户服务        |
|  (Gateway)       |    |  (Auth)          |    |  (User)          |
|                  |    |                  |    |                  |
+--------+---------+    +------------------+    +------------------+
         |
         v
+--------+---------+    +------------------+    +------------------+
|                  |    |                  |    |                  |
|  车辆服务        |<-->|  维修保养服务    |<-->|  预约服务        |
|  (Vehicle)       |    |  (Maintenance)   |    |  (Appointment)   |
|                  |    |                  |    |                  |
+--------+---------+    +------------------+    +------------------+
         |
         v
+--------+---------+    +------------------+    +------------------+
|                  |    |                  |    |                  |
|  数据库          |    |  缓存            |    |  消息队列        |
|  (MySQL)         |    |  (Redis)         |    |  (RabbitMQ)      |
|                  |    |                  |    |                  |
+------------------+    +------------------+    +------------------+
```

## 功能模块

### 用户管理模块
- 用户注册、登录、找回密码
- 用户信息管理
- 角色权限管理

### 车辆管理模块
- 车辆信息管理
- 车辆状态监控
- 车辆档案管理

### 维修保养模块
- 维修记录管理
- 保养提醒
- 维修工单处理

### 预约试驾模块
- 试驾预约
- 预约审核
- 试驾反馈

### 系统管理模块
- 系统配置
- 日志管理
- 数据备份与恢复

## 数据库设计

详见 `docs/database-design.md`